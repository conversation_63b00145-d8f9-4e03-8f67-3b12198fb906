/**
 * Integrated App Builder
 * 
 * Main App Builder component that integrates all UI/UX improvements
 * with existing features including WebSocket collaboration, template system,
 * code export, tutorial assistant, and AI design suggestions.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Layout, message, Spin, Alert, Modal } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import styled from 'styled-components';

// Enhanced UI/UX Components
import ResponsiveAppLayout from '../layout/ResponsiveAppLayout';
import AccessibleComponent from '../common/AccessibleComponent';
import EnhancedComponentPaletteFixed from './EnhancedComponentPaletteFixed';
import UXEnhancedPropertyEditor from './UXEnhancedPropertyEditor';
import UXEnhancedPreviewArea from './UXEnhancedPreviewArea';
import EnhancedKeyboardShortcuts from './EnhancedKeyboardShortcuts';
import { DragDropProvider } from '../dragdrop/EnhancedDragDropSystem';

// Progressive Loading
import { useProgressiveLoading, ProgressiveWrapper } from '../../utils/progressiveLoading';

// Lazy-loaded Feature Components
import {
  TutorialAssistant,
  AIDesignSuggestions,
  TemplateManager,
  CodeExporter,
  CollaborationIndicator
} from '../../config/lazyComponents';

// Import new components
import AppBuilderExamples from '../examples/AppBuilderExamples';
import IntegratedTutorialSystem from '../tutorial/IntegratedTutorialSystem';

// Design System
import { theme, visualHierarchy } from '../../design-system';

// Hooks and Services
import useWebSocket from '../../hooks/useWebSocket';
import useAppBuilder from '../../hooks/useAppBuilder';
import useTutorial from '../../hooks/useTutorial';
import useAIDesignSuggestions from '../../hooks/useAIDesignSuggestions';
import useTemplates from '../../hooks/useTemplates';
import useCodeExport from '../../hooks/useCodeExport';
import useCollaboration from '../../hooks/useCollaboration';

const IntegratedContainer = styled.div`
  height: 100vh;
  background: ${theme.colors.background.default};
  position: relative;
  overflow: hidden;
  
  /* Ensure proper stacking context */
  z-index: 0;
`;

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[4]};
  flex: 1;
  
  .app-title {
    ${visualHierarchy.typography.heading.h4};
    margin: 0;
    color: ${theme.colors.text.primary};
  }
  
  .project-name {
    ${visualHierarchy.typography.body.small};
    color: ${theme.colors.text.secondary};
    margin-left: ${theme.spacing[2]};
  }
`;

const FeatureToggles = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[2]};
  
  ${theme.mediaQueries.maxMd} {
    gap: ${theme.spacing[1]};
  }
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: ${theme.zIndex.modal};
  
  .loading-text {
    margin-top: ${theme.spacing[4]};
    ${visualHierarchy.typography.body.medium};
    color: ${theme.colors.text.secondary};
  }
`;

const ErrorBoundary = styled.div`
  padding: ${theme.spacing[8]};
  text-align: center;
  
  .error-title {
    ${visualHierarchy.typography.heading.h3};
    color: ${theme.colors.error.main};
    margin-bottom: ${theme.spacing[4]};
  }
  
  .error-message {
    ${visualHierarchy.typography.body.medium};
    color: ${theme.colors.text.secondary};
    margin-bottom: ${theme.spacing[6]};
  }
`;

export default function IntegratedAppBuilder({
  projectId,
  initialComponents = [],
  enableFeatures = {
    websocket: true,
    tutorial: true,
    aiSuggestions: true,
    templates: true,
    codeExport: true,
    collaboration: true,
  },
  onSave,
  onLoad,
  onError,
}) {
  // Redux state
  const dispatch = useDispatch();
  const user = useSelector(state => state.auth?.user);
  const project = useSelector(state => state.projects?.current);

  // Local state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [previewMode, setPreviewMode] = useState(false);

  // Clipboard for copy/paste operations
  const [clipboard, setClipboard] = useState(null);

  // Feature states
  const [showTutorial, setShowTutorial] = useState(false);
  const [showAISuggestions, setShowAISuggestions] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [showCodeExport, setShowCodeExport] = useState(false);
  const [showExamples, setShowExamples] = useState(false);

  // Progressive loading for feature components
  const progressiveLoading = useProgressiveLoading({
    strategy: 'priority',
    features: Object.keys(enableFeatures).filter(key => enableFeatures[key]),
    autoStart: true,
    delay: 1000 // Start loading after initial render
  });

  // App Builder hook with enhanced features
  const {
    components,
    addComponent,
    updateComponent,
    deleteComponent,
    moveComponent,
    duplicateComponent,
    undoAction,
    redoAction,
    canUndo,
    canRedo,
    saveProject,
    loadProject,
    isModified,
  } = useAppBuilder({
    projectId,
    initialComponents,
    autoSave: true,
    onSave,
    onLoad,
    onError: (err) => {
      setError(err);
      if (onError) onError(err);
    },
  });

  // WebSocket collaboration
  const {
    isConnected: websocketConnected,
    collaborators,
    sendUpdate,
    sendCursor,
  } = useWebSocket({
    enabled: enableFeatures.websocket,
    projectId,
    userId: user?.id,
    onComponentUpdate: updateComponent,
    onComponentAdd: addComponent,
    onComponentDelete: deleteComponent,
    onComponentMove: moveComponent,
  });

  // Tutorial system
  const {
    isActive: tutorialActive,
    currentStep,
    totalSteps,
    nextStep,
    previousStep,
    skipTutorial,
    startTutorial,
  } = useTutorial({
    enabled: enableFeatures.tutorial,
    autoStart: !user?.hasCompletedTutorial,
  });

  // AI Design Suggestions
  const {
    suggestions,
    loading: aiLoading,
    generateSuggestions,
    applySuggestion,
    dismissSuggestion,
  } = useAIDesignSuggestions({
    enabled: enableFeatures.aiSuggestions,
    components,
    selectedComponent,
    autoRefresh: true,
  });

  // Template system
  const {
    templates,
    loading: templatesLoading,
    saveAsTemplate,
    loadTemplate,
    deleteTemplate,
  } = useTemplates({
    enabled: enableFeatures.templates,
    projectId,
  });

  // Code export
  const {
    exportCode,
    exportFormats,
    loading: exportLoading,
    downloadCode,
  } = useCodeExport({
    enabled: enableFeatures.codeExport,
    components,
    projectSettings: project?.settings,
  });

  // Collaboration features
  const {
    activeUsers,
    comments,
    addComment,
    resolveComment,
    shareProject,
  } = useCollaboration({
    enabled: enableFeatures.collaboration,
    projectId,
    websocketConnected,
  });

  // Initialize app
  useEffect(() => {
    const initializeApp = async () => {
      try {
        setLoading(true);

        if (projectId) {
          await loadProject(projectId);
        }

        // Initialize features
        if (enableFeatures.aiSuggestions) {
          await generateSuggestions();
        }

        setLoading(false);
      } catch (err) {
        setError(err);
        setLoading(false);
      }
    };

    initializeApp();
  }, [projectId, enableFeatures.aiSuggestions]); // Removed function dependencies to prevent infinite re-renders

  // Handle component selection with collaboration
  const handleSelectComponent = useCallback((component) => {
    setSelectedComponent(component);

    // Send cursor position for collaboration
    if (websocketConnected && component) {
      sendCursor({
        componentId: component.id,
        action: 'select',
        timestamp: Date.now(),
      });
    }
  }, [websocketConnected, sendCursor]);

  // Handle component updates with real-time sync
  const handleUpdateComponent = useCallback((componentId, updates) => {
    updateComponent(componentId, updates);

    // Send update to collaborators
    if (websocketConnected) {
      sendUpdate({
        type: 'component_update',
        componentId,
        updates,
        userId: user?.id,
        timestamp: Date.now(),
      });
    }
  }, [updateComponent, websocketConnected, sendUpdate, user?.id]);

  // Handle component addition with AI suggestions
  const handleAddComponent = useCallback(async (componentType, position) => {
    const newComponent = await addComponent(componentType, position);

    // Generate AI suggestions for the new component
    if (enableFeatures.aiSuggestions && newComponent) {
      setTimeout(() => generateSuggestions(), 500);
    }

    // Send update to collaborators
    if (websocketConnected) {
      sendUpdate({
        type: 'component_add',
        component: newComponent,
        userId: user?.id,
        timestamp: Date.now(),
      });
    }

    return newComponent;
  }, [addComponent, enableFeatures.aiSuggestions, generateSuggestions, websocketConnected, sendUpdate, user?.id]);

  // Handle component deletion with confirmation
  const handleDeleteComponent = useCallback((componentId) => {
    const component = components.find(c => c.id === componentId);

    if (component) {
      deleteComponent(componentId);

      // Clear selection if deleted component was selected
      if (selectedComponent?.id === componentId) {
        setSelectedComponent(null);
      }

      // Send update to collaborators
      if (websocketConnected) {
        sendUpdate({
          type: 'component_delete',
          componentId,
          userId: user?.id,
          timestamp: Date.now(),
        });
      }

      message.success(`Deleted ${component.type} component`);
    }
  }, [components, deleteComponent, selectedComponent, websocketConnected, sendUpdate, user?.id]);

  // Enhanced keyboard shortcut handlers
  const handleKeyboardAction = useCallback((action, shortcutKey) => {
    switch (action) {
      case 'save':
        if (isModified) {
          saveProject();
          message.success('Project saved successfully');
        }
        break;

      case 'copy':
        if (selectedComponent) {
          setClipboard({ ...selectedComponent, id: undefined });
          message.success('Component copied to clipboard');
        }
        break;

      case 'paste':
        if (clipboard) {
          const newComponent = {
            ...clipboard,
            id: `component_${Date.now()}`,
            position: {
              x: (clipboard.position?.x || 0) + 20,
              y: (clipboard.position?.y || 0) + 20
            }
          };
          handleAddComponent(newComponent.type, newComponent.position);
          message.success('Component pasted from clipboard');
        }
        break;

      case 'cut':
        if (selectedComponent) {
          setClipboard({ ...selectedComponent, id: undefined });
          handleDeleteComponent(selectedComponent.id);
          message.success('Component cut to clipboard');
        }
        break;

      case 'undo':
        if (canUndo) {
          undoAction();
          message.success('Action undone');
        }
        break;

      case 'redo':
        if (canRedo) {
          redoAction();
          message.success('Action redone');
        }
        break;

      case 'delete':
        if (selectedComponent) {
          handleDeleteComponent(selectedComponent.id);
        }
        break;

      case 'preview':
        setPreviewMode(prev => !prev);
        message.success(`Preview mode ${!previewMode ? 'enabled' : 'disabled'}`);
        break;

      case 'new':
        handleAddComponent('text', { x: 100, y: 100 });
        break;

      case 'fullscreen':
        if (document.fullscreenElement) {
          document.exitFullscreen();
        } else {
          document.documentElement.requestFullscreen();
        }
        break;

      default:
        console.log(`Unhandled keyboard action: ${action}`);
    }
  }, [
    selectedComponent,
    clipboard,
    isModified,
    canUndo,
    canRedo,
    previewMode,
    saveProject,
    undoAction,
    redoAction,
    handleAddComponent,
    handleDeleteComponent
  ]);

  // Memoized header content
  const headerContent = useMemo(() => (
    <HeaderContent>
      <div>
        <h1 className="app-title">App Builder</h1>
        {project?.name && (
          <span className="project-name">{project.name}</span>
        )}

        {/* Status Indicators */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          marginTop: '4px',
          fontSize: '12px'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            padding: '2px 8px',
            borderRadius: '12px',
            background: websocketConnected ? '#f6ffed' : '#fff2f0',
            border: `1px solid ${websocketConnected ? '#b7eb8f' : '#ffccc7'}`,
            color: websocketConnected ? '#52c41a' : '#ff4d4f'
          }}>
            <div style={{
              width: '6px',
              height: '6px',
              borderRadius: '50%',
              background: websocketConnected ? '#52c41a' : '#ff4d4f'
            }} />
            {websocketConnected ? 'Connected' : 'Offline'}
          </div>

          {isModified && (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              padding: '2px 8px',
              borderRadius: '12px',
              background: '#fff7e6',
              border: '1px solid #ffd591',
              color: '#fa8c16'
            }}>
              <div style={{
                width: '6px',
                height: '6px',
                borderRadius: '50%',
                background: '#fa8c16'
              }} />
              Unsaved Changes
            </div>
          )}

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            padding: '2px 8px',
            borderRadius: '12px',
            background: '#f0f5ff',
            border: '1px solid #adc6ff',
            color: '#1890ff'
          }}>
            {components.length} Component{components.length !== 1 ? 's' : ''}
          </div>
        </div>
      </div>

      <FeatureToggles>
        {enableFeatures.collaboration && (
          <ProgressiveWrapper
            componentName="CollaborationIndicator"
            strategy="viewport"
            fallback={<div style={{ width: 32, height: 32 }} />}
          >
            <CollaborationIndicator
              connected={websocketConnected}
              collaborators={collaborators}
              activeUsers={activeUsers}
            />
          </ProgressiveWrapper>
        )}

        {enableFeatures.aiSuggestions && (
          <ProgressiveWrapper
            componentName="AIDesignSuggestions"
            strategy="interaction"
            fallback={<div style={{ width: 120, height: 32 }} />}
          >
            <AIDesignSuggestions
              suggestions={suggestions}
              loading={aiLoading}
              onApply={applySuggestion}
              onDismiss={dismissSuggestion}
              compact
            />
          </ProgressiveWrapper>
        )}

        {enableFeatures.templates && (
          <div data-tutorial="theme-manager">
            <ProgressiveWrapper
              componentName="TemplateManager"
              strategy="interaction"
              fallback={<div style={{ width: 100, height: 32 }} />}
            >
              <TemplateManager
                templates={templates}
                loading={templatesLoading}
                onSave={saveAsTemplate}
                onLoad={loadTemplate}
                onDelete={deleteTemplate}
                compact
              />
            </ProgressiveWrapper>
          </div>
        )}

        {enableFeatures.codeExport && (
          <ProgressiveWrapper
            componentName="CodeExporter"
            strategy="interaction"
            fallback={<div style={{ width: 100, height: 32 }} />}
          >
            <CodeExporter
              formats={exportFormats}
              loading={exportLoading}
              onExport={exportCode}
              onDownload={downloadCode}
              compact
            />
          </ProgressiveWrapper>
        )}

        {/* Preview Mode Toggle */}
        <button
          data-tutorial="preview-mode"
          onClick={() => setPreviewMode(prev => !prev)}
          style={{
            background: previewMode ? '#52c41a' : '#f0f0f0',
            color: previewMode ? 'white' : '#333',
            border: 'none',
            borderRadius: '6px',
            padding: '8px 16px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            transition: 'all 0.2s ease',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
          }}
          title={previewMode ? 'Exit preview mode' : 'Enter preview mode'}
        >
          👁️ {previewMode ? 'Exit Preview' : 'Preview'}
        </button>

        {/* Examples Button */}
        <button
          onClick={() => setShowExamples(true)}
          style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            padding: '8px 16px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            transition: 'all 0.2s ease',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
          }}
          onMouseOver={(e) => {
            e.target.style.transform = 'translateY(-1px)';
            e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
          }}
          onMouseOut={(e) => {
            e.target.style.transform = 'translateY(0)';
            e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
          }}
          title="View examples and tutorials"
        >
          📚 Examples
        </button>
      </FeatureToggles>
    </HeaderContent>
  ), [
    project?.name,
    enableFeatures,
    websocketConnected,
    collaborators,
    activeUsers,
    suggestions,
    aiLoading,
    applySuggestion,
    dismissSuggestion,
    templates,
    templatesLoading,
    saveAsTemplate,
    loadTemplate,
    deleteTemplate,
    exportFormats,
    exportLoading,
    exportCode,
    downloadCode,
    setShowExamples,
    previewMode,
    setPreviewMode,
    components.length,
    isModified,
  ]);

  // Error boundary
  if (error) {
    return (
      <IntegratedContainer>
        <ErrorBoundary>
          <div className="error-title">Something went wrong</div>
          <div className="error-message">{error.message}</div>
          <button onClick={() => window.location.reload()}>
            Reload Application
          </button>
        </ErrorBoundary>
      </IntegratedContainer>
    );
  }

  return (
    <IntegratedContainer>
      <DragDropProvider showOverlay={true}>
        <AccessibleComponent
          role="application"
          ariaLabel="App Builder application for creating user interfaces"
        >
          <ResponsiveAppLayout
            headerContent={headerContent}
            leftPanel={
              <div data-tutorial="component-palette">
                <EnhancedComponentPaletteFixed
                  onAddComponent={handleAddComponent}
                  selectedComponent={selectedComponent}
                  showAISuggestions={enableFeatures.aiSuggestions}
                  loading={loading}
                />
              </div>
            }
            rightPanel={
              <div data-tutorial="property-editor">
                <UXEnhancedPropertyEditor
                  component={selectedComponent}
                  onUpdateComponent={handleUpdateComponent}
                  loading={loading}
                />
              </div>
            }
            showBreakpointIndicator={process.env.NODE_ENV === 'development'}
            enablePanelResize={true}
            persistLayout={true}
          >
            <div data-tutorial="canvas-area">
              <UXEnhancedPreviewArea
                components={components}
                selectedComponentId={selectedComponent?.id}
                onSelectComponent={handleSelectComponent}
                onDeleteComponent={handleDeleteComponent}
                onUpdateComponent={handleUpdateComponent}
                onMoveComponent={moveComponent}
                previewMode={previewMode}
                websocketConnected={websocketConnected}
                loading={loading}
                onDrop={handleAddComponent}
              />
            </div>
          </ResponsiveAppLayout>
        </AccessibleComponent>
      </DragDropProvider>

      {/* Enhanced Keyboard Shortcuts */}
      <EnhancedKeyboardShortcuts
        onAction={handleKeyboardAction}
        showQuickActions={true}
        enableCustomization={true}
        showFeedback={true}
      />

      {/* Tutorial Overlay */}
      {enableFeatures.tutorial && tutorialActive && (
        <TutorialAssistant
          currentStep={currentStep}
          totalSteps={totalSteps}
          onNext={nextStep}
          onPrevious={previousStep}
          onSkip={skipTutorial}
          onComplete={skipTutorial}
        />
      )}

      {/* Integrated Tutorial System */}
      {enableFeatures.tutorial && (
        <IntegratedTutorialSystem
          onTutorialComplete={() => {
            message.success('Tutorial completed! You\'re ready to build amazing apps.');
          }}
        >
          {/* This wraps the entire app for tutorial overlays */}
        </IntegratedTutorialSystem>
      )}

      {/* Examples Modal */}
      {showExamples && (
        <Modal
          title="App Builder Examples & Tutorials"
          visible={showExamples}
          onCancel={() => setShowExamples(false)}
          footer={null}
          width="90%"
          style={{ top: 20 }}
          bodyStyle={{ padding: 0, height: '80vh', overflow: 'auto' }}
        >
          <AppBuilderExamples />
        </Modal>
      )}

      {/* Loading Overlay */}
      {loading && (
        <LoadingOverlay>
          <Spin size="large" />
          <div className="loading-text">Loading App Builder...</div>
        </LoadingOverlay>
      )}

      {/* Development Tools */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          position: 'fixed',
          bottom: theme.spacing[4],
          left: theme.spacing[4],
          zIndex: theme.zIndex.tooltip,
        }}>
          <Alert
            message={`Components: ${components.length} | Modified: ${isModified ? 'Yes' : 'No'} | Connected: ${websocketConnected ? 'Yes' : 'No'}`}
            type="info"
            size="small"
            showIcon={false}
          />
        </div>
      )}
    </IntegratedContainer>
  );
}
